import { useState } from 'react';
import { CertificateFileManager } from './CertificateFileManager';

// Define certificate status types (page-based status system)
type CertificateStatus = 'objektdaten' | 'gebaeudedetails1' | 'gebaeudedetails2' | 'fenster' | 'heizung' | 'tww-lueftung' | 'verbrauch' | 'zusammenfassung';

// Define certificate type
type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

interface Energieausweis {
  id: string;
  updated_at: string | null;
  certificate_type: CertificateType | null;
  payment_status: string | null;
  status: CertificateStatus;
  order_number: string | null;
  user_id: string | null;
}

interface ExpandableTableRowProps {
  certificate: Energieausweis;
  children: React.ReactNode; // The original row content
}

export const ExpandableTableRow = ({ certificate, children }: ExpandableTableRowProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <>
      {/* Main Row */}
      <tr className={`${isExpanded ? 'bg-blue-50' : ''} hover:bg-gray-50 transition-colors`}>
        {/* Expand/Collapse Button Cell */}
        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
          <button
            onClick={toggleExpanded}
            className="inline-flex items-center justify-center w-6 h-6 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 rounded"
            aria-label={isExpanded ? 'Zeile einklappen' : 'Zeile ausklappen'}
          >
            <svg
              className={`w-4 h-4 transform transition-transform duration-200 ${
                isExpanded ? 'rotate-90' : ''
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </td>
        
        {/* Original Row Content */}
        {children}
      </tr>

      {/* Expanded Content Row */}
      {isExpanded && (
        <tr className="bg-white">
          <td colSpan={6} className="px-0 py-0">
            <div className="border-l-4 border-blue-200">
              <CertificateFileManager
                userId={certificate.user_id}
                certificateId={certificate.id}
                orderNumber={certificate.order_number}
              />
            </div>
          </td>
        </tr>
      )}
    </>
  );
};
